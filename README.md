# Robust API Engine

A comprehensive, production-ready API engine with credit-based usage tracking, tier-based access control, and real-time WebSocket streaming capabilities.

## 🚀 Features

- **Credit-based Usage System** - Track and control API usage with flexible credit allocation
- **Multi-tier Access Control** - Free, Basic, Premium, and Enterprise tiers with different permissions
- **RESTful API Endpoints** - Comprehensive GET/POST endpoints with proper authentication
- **WebSocket Server** - Real-time streaming with subscribe/unsubscribe functionality
- **Redis Pub/Sub Integration** - Distributed event handling and high-performance caching
- **Advanced Rate Limiting** - Intelligent rate limiting based on user tiers and endpoints
- **Dual Authentication** - Support for both JWT tokens and API keys
- **Comprehensive Logging** - Detailed usage analytics and monitoring
- **Database Functions** - PostgreSQL stored procedures for efficient credit management
- **Graceful Shutdown** - Proper cleanup of connections and resources

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Client Apps   │    │   WebSocket     │    │   Admin Panel   │
│                 │    │   Clients       │    │                 │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────┴─────────────┐
                    │     API Gateway           │
                    │   (Express.js Server)     │
                    └─────────────┬─────────────┘
                                 │
                    ┌─────────────┴─────────────┐
                    │     Middleware Layer      │
                    │  • Authentication         │
                    │  • Rate Limiting          │
                    │  • Credit Validation      │
                    │  • Logging               │
                    └─────────────┬─────────────┘
                                 │
        ┌────────────────────────┼────────────────────────┐
        │                       │                        │
┌───────┴────────┐    ┌─────────┴─────────┐    ┌─────────┴─────────┐
│  PostgreSQL    │    │     Redis         │    │   WebSocket       │
│  Database      │    │   • Caching       │    │   Server          │
│  • Users       │    │   • Pub/Sub       │    │   • Streams       │
│  • Credits     │    │   • Rate Limits   │    │   • Subscriptions │
│  • Usage Logs  │    │   • Sessions      │    │   • Real-time     │
└────────────────┘    └───────────────────┘    └───────────────────┘
```

## 📋 Prerequisites

- Node.js 18+ 
- PostgreSQL 12+
- Redis 6+
- npm or pnpm

## 🛠️ Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd nodejs-api
   ```

2. **Install dependencies**
   ```bash
   pnpm install
   ```

3. **Environment setup**
   ```bash
   cp .env.example .env
   # Edit .env with your database and Redis credentials
   ```

4. **Database setup**
   ```bash
   # Create your PostgreSQL database
   createdb api_engine
   
   # Run migrations
   npm run db:migrate
   ```

5. **Start the server**
   ```bash
   # Development
   npm run dev
   
   # Production
   npm start
   ```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `NODE_ENV` | Environment mode | `development` |
| `PORT` | Server port | `3000` |
| `DB_HOST` | PostgreSQL host | `localhost` |
| `DB_PORT` | PostgreSQL port | `5432` |
| `DB_NAME` | Database name | `api_engine` |
| `DB_USER` | Database user | `postgres` |
| `DB_PASSWORD` | Database password | - |
| `REDIS_HOST` | Redis host | `localhost` |
| `REDIS_PORT` | Redis port | `6379` |
| `JWT_SECRET` | JWT signing secret | - |
| `JWT_EXPIRES_IN` | JWT expiration | `24h` |

### Access Tiers

| Tier | Credits/Month | Requests/Min | WebSocket Connections | Price | Status |
|------|---------------|--------------|----------------------|-------|--------|
| **Free** | 1,000 | 10 | 1 | $0.00 | Disabled* |
| **Basic** | 1,000,000 | 60 | 3 | $49.99 | Enabled |
| **Premium** | 5,000,000 | 300 | 5 | $149.99 | Enabled |
| **Enterprise** | Unlimited | 1,000 | 10 | $499.99 | Enabled |

*The free tier is currently disabled but can be enabled through admin controls.

## 📚 API Documentation

### Interactive Documentation

The API includes **Scalar-powered interactive documentation** hosted directly within the application:

```bash
# Start the server
pnpm start

# Open documentation in browser
pnpm run docs:open

# Or visit directly
open http://localhost:3001/docs
```

**Documentation Features:**
- 🎯 **Interactive API Explorer** - Test endpoints directly in the browser
- 📖 **Complete API Reference** - All endpoints, parameters, and responses
- 🔧 **Built-in Authentication** - Test with your API keys
- 📱 **Responsive Design** - Works on desktop and mobile
- ⚡ **Real-time Updates** - No caching issues, updates instantly
- 🎨 **Beautiful UI** - Modern, professional interface

**Documentation Endpoints:**
- `GET /docs` - Interactive documentation interface
- `GET /docs/openapi.yaml` - OpenAPI specification (YAML)
- `GET /docs/openapi.json` - OpenAPI specification (JSON)
- `GET /docs/info` - Documentation metadata

### API Usage

#### Authentication

```bash
# Register a new user (admin-only endpoint)
curl -X POST http://localhost:3001/admin/users/register \
  -H "Content-Type: application/json" \
  -H "X-Admin-API-Key: YOUR_ADMIN_KEY" \
  -d '{"email":"<EMAIL>","password":"password123"}'

# Login (admin-only endpoint)
curl -X POST http://localhost:3001/admin/users/login \
  -H "Content-Type: application/json" \
  -H "X-Admin-API-Key: YOUR_ADMIN_KEY" \
  -d '{"email":"<EMAIL>","password":"password123"}'
```

#### API Endpoints

```bash


# KOL Feed History (3 credits)
curl -X GET http://localhost:3001/api/v1/kol-feed/history?limit=10 \
  -H "X-API-Key: YOUR_API_KEY"

# WebSocket connection info
curl -X GET http://localhost:3001/ws-api/info \
  -H "X-API-Key: YOUR_API_KEY"

# Available streams
curl -X GET http://localhost:3001/ws-api/streams \
  -H "X-API-Key: YOUR_API_KEY"
```

#### WebSocket Connection

```javascript
// Connect with API key
const ws = new WebSocket('ws://localhost:3001/ws?apiKey=YOUR_API_KEY');

// Subscribe to KOL feed stream
ws.send(JSON.stringify({
  type: 'subscribe',
  payload: { stream: 'kol-feed' }
}));

// Handle incoming messages
ws.onmessage = (event) => {
  const message = JSON.parse(event.data);

  if (message.type === 'stream_data' && message.stream === 'kol-feed') {
    console.log('KOL Trade:', message.data);
    console.log('KOL:', message.data.kol_label);
    console.log('Transaction:', message.data.transactionType);
    console.log('Token In:', message.data.tokenIn.symbol);
    console.log('Token Out:', message.data.tokenOut.symbol);
  }
};
```

## 💳 Credit System

The API uses a sophisticated credit-based usage tracking system for fair resource allocation and billing.

### How It Works

**Credit Consumption:**
- Each API call consumes credits based on complexity
- Credits are deducted **before** processing the request
- Atomic operations prevent race conditions
- Real-time credit tracking and validation

**Credit Allocation by Tier:**

| Tier | Monthly Credits | Cost | Behavior |
|------|----------------|------|----------|
| **Free** | 1,000 | $0 | Hard limit, requests blocked when exceeded |
| **Basic** | 10,000 | $9.99 | Hard limit, requests blocked when exceeded |
| **Premium** | 100,000 | $49.99 | Hard limit, requests blocked when exceeded |
| **Enterprise** | Unlimited | $199.99 | No credit consumption tracking |

### Credit Costs

| Endpoint | Credits | Description |
|----------|---------|-------------|
| `GET /api/v1/demo` | 1 | Basic demo functionality |
| `GET /api/v1/data` | 2 | Data retrieval |
| `GET /api/v1/analytics` | 5 | Analytics processing |
| `GET /api/v1/search` | 3 | Search functionality |
| `POST /api/v1/batch` | 10 | Batch processing |
| WebSocket | 0 | Real-time streaming (no cost) |

### Configuration

**Setting Credit Costs** (`src/middleware/credits.js`):
```javascript
const CREDIT_COSTS = {
    '/api/v1/demo': 1,
    '/api/v1/data': 2,
    '/api/v1/analytics': 5,
    '/api/v1/search': 3,
    '/api/v1/batch': 10
};
```

**Tier Credit Limits** (Database - `access_tiers` table):
```sql
-- Update tier credit limits
UPDATE access_tiers
SET max_credits_per_month = 5000000
WHERE name = 'premium';
```

### Monitoring Credits



**Admin Credit Management:**
```bash
# View user credits (admin)
curl -X GET http://localhost:3001/admin/users/{userId}/credits \
  -H "X-Admin-API-Key: YOUR_ADMIN_API_KEY"

# Add credits to user (admin)
curl -X POST http://localhost:3001/admin/users/{userId}/credits \
  -H "X-Admin-API-Key: YOUR_ADMIN_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{"credits_to_add": 1000, "reason": "Bonus credits"}'
```

### Credit Responses

**Successful Request:**
```json
{
  "success": true,
  "data": {...},
  "credits_remaining": 9949,
  "credits_used": 1
}
```

**Insufficient Credits:**
```json
{
  "error": "Insufficient credits",
  "code": "INSUFFICIENT_CREDITS",
  "credits_remaining": 0,
  "credits_required": 5
}
```

## 🧪 Testing

### Demo User

A demo user is created during migration:
- **Email**: `<EMAIL>`
- **Password**: `demo123`
- **Tier**: Basic (1,000,000 credits)

### Automated Testing

```bash
# API functionality tests
npm run test:api

# WebSocket streaming tests
npm run test:websocket

# System requirements check
npm run setup-check
```

### Postman Collection

Import the Postman collections for interactive testing:
- **[User API Collection](postman/StalkAPI_User_Collection.json)** - End-user API testing
- **[Admin API Collection](postman/StalkAPI_Admin_Collection.json)** - Internal management
- **[Environment Variables](postman/StalkAPI_Postman_Environment.json)** - Configuration settings
- **[Postman Guide](docs/POSTMAN_GUIDE.md)** - Detailed usage guide

**Quick Start:**
1. Import both JSON files into Postman
2. Select "StalkAPI Environment"
3. Run "Login" request to get JWT token
4. Test any endpoint with automatic authentication

### Tier Management

You can enable/disable tiers through the admin API or directly in the database:

```sql
-- Enable free tier
UPDATE access_tiers SET is_enabled = true WHERE name = 'free';

-- Disable free tier
UPDATE access_tiers SET is_enabled = false WHERE name = 'free';

-- Check tier status
SELECT name, is_enabled FROM access_tiers ORDER BY id;
```

**Admin API endpoints** (Admin API key required):
- `GET /admin/tiers` - List all tiers
- `POST /admin/tiers/{id}/enable` - Enable a tier
- `POST /admin/tiers/{id}/disable` - Disable a tier
- `PUT /admin/tiers/{id}` - Update tier configuration
- `GET /admin/me` - Get admin user info
- `GET /admin/admins` - List all admin users (system admin only)
- `POST /admin/admins` - Create admin user (system admin only)

### Health Check

```bash
curl http://localhost:3000/health
```

### WebSocket Test

```bash
# Get WebSocket connection info
curl -X GET http://localhost:3000/ws-api/info \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## 📊 Monitoring

### Usage Statistics

```bash
# Get user usage stats
curl -X GET http://localhost:3000/auth/usage?days=30 \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# WebSocket statistics
curl -X GET http://localhost:3000/ws-api/stats?days=7 \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Available Streams

- **demo-stream**: Basic demo data (Free+)
- **data-stream**: Market-like data (Basic+)
- **analytics-stream**: System metrics (Premium+)
- **enterprise-stream**: High-frequency data (Enterprise)

## 🔒 Security Features

- **Helmet.js** - Security headers
- **CORS** - Cross-origin resource sharing
- **Rate Limiting** - Tier-based request limits
- **Input Validation** - Request payload validation
- **Password Hashing** - bcrypt with configurable rounds
- **JWT Tokens** - Secure authentication
- **API Key Authentication** - Alternative auth method

## 🚀 Production Deployment

### Nginx + Cloudflare (Recommended)

For production deployment with nginx reverse proxy and Cloudflare:
- **[Nginx Deployment Guide](docs/NGINX_DEPLOYMENT.md)** - Complete setup guide
- **[nginx.config](nginx.config)** - Production nginx configuration

**Features:**
- Domain: https://data.stalkapi.com
- SSL with Cloudflare origin certificates
- Real IP detection from Cloudflare headers
- Rate limiting and security headers
- WebSocket support

### Docker (Alternative)

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 3000
CMD ["npm", "start"]
```

### Environment Variables for Production

```bash
NODE_ENV=production
JWT_SECRET=your-super-secure-secret-key
DB_SSL=true
REDIS_PASSWORD=your-redis-password
```

### Database Optimization

- Enable connection pooling
- Set up read replicas for analytics
- Configure proper indexes (included in migration)
- Set up automated backups

### Redis Configuration

- Enable persistence
- Configure memory limits
- Set up Redis Cluster for high availability
- Monitor memory usage

## 📈 Performance

### Caching Strategy

- **User Data**: 5 minutes
- **Analytics**: 5 minutes
- **Search Results**: 2 minutes
- **Rate Limits**: Real-time with Redis

### Database Optimization

- Indexed queries for fast lookups
- Stored procedures for credit operations
- Connection pooling
- Query optimization

### WebSocket Performance

- Connection limits per tier
- Heartbeat monitoring
- Automatic cleanup of stale connections
- Redis pub/sub for scalability

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the ISC License.

## 📚 Documentation

### Core Documentation
- **[API Documentation](docs/API_DOCUMENTATION.md)** - Complete API reference with examples
- **[Setup Guide](docs/SETUP_GUIDE.md)** - Installation and configuration instructions
- **[Project Summary](docs/PROJECT_SUMMARY.md)** - Comprehensive project overview and architecture

### Specialized Guides
- **[Credit System Guide](docs/CREDIT_SYSTEM_GUIDE.md)** - Complete credit system documentation
- **[Postman Guide](docs/POSTMAN_GUIDE.md)** - API testing with Postman collections
- **[Nginx Deployment](docs/NGINX_DEPLOYMENT.md)** - Production deployment with Cloudflare

### Quick Links
- **[Postman Collections](postman/)** - Complete testing collections for API
- **[User API Collection](postman/StalkAPI_User_Collection.json)** - End-user API testing
- **[Admin API Collection](postman/StalkAPI_Admin_Collection.json)** - Internal management
- **[Environment Variables](postman/StalkAPI_Postman_Environment.json)** - Configuration for Postman

## 🆘 Support

For support and questions:
- Check the [API Documentation](docs/API_DOCUMENTATION.md)
- Review the health check endpoint
- Check application logs
- Monitor Redis and PostgreSQL connections

## 🔄 Changelog

### v1.0.0
- Initial release
- Credit-based usage system
- Multi-tier access control
- WebSocket streaming
- Redis pub/sub integration
- Comprehensive API endpoints
